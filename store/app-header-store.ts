import { create } from 'zustand';
import { LucideIcon } from 'lucide-react';

export type HeaderVariant = 'default' | 'dashboard' | 'management' | 'analytics' | 'settings';

export interface HeaderTabItem {
  id: string;
  label: string;
  icon?: LucideIcon;
  disabled?: boolean;
  badge?: string | number;
}

interface AppHeaderState {
  title?: string;
  description?: string;
  breadcrumbs?: { label: string; url: string }[];
  actions?: React.ReactNode[];
  variant?: HeaderVariant;
  isLoading?: boolean;
  showBackButton?: boolean;
  backUrl?: string;
  tabs?: HeaderTabItem[];
  activeTab?: string;
  showTabs?: boolean;
}

interface AppHeaderActions {
  setHeaderContent: (content: AppHeaderState) => void;
  resetHeaderContent: () => void;
  setLoading: (loading: boolean) => void;
  updateActions: (actions: React.ReactNode[]) => void;
  setVariant: (variant: HeaderVariant) => void;
  setTabs: (tabs: HeaderTabItem[], activeTab?: string) => void;
  setActiveTab: (tabId: string) => void;
  updateTab: (tabId: string, updates: Partial<HeaderTabItem>) => void;
  hideTabs: () => void;
}

type AppHeaderStore = AppHeaderState & AppHeaderActions;

const initialState: AppHeaderState = {
  title: undefined,
  description: undefined,
  breadcrumbs: undefined,
  actions: undefined,
  variant: 'default',
  isLoading: false,
  showBackButton: false,
  backUrl: undefined,
  tabs: undefined,
  activeTab: undefined,
  showTabs: false,
};

export const useAppHeaderStore = create<AppHeaderStore>((set, get) => ({
  ...initialState,
  setHeaderContent: (content) => set({ ...content }),
  resetHeaderContent: () => set(initialState),
  setLoading: (loading) => set({ isLoading: loading }),
  updateActions: (actions) => set({ actions }),
  setVariant: (variant) => set({ variant }),

  setTabs: (tabs, activeTab) => {
    const defaultActiveTab = activeTab || tabs[0]?.id;
    console.log('Store: Setting tabs', tabs.map(t => t.label), 'activeTab:', defaultActiveTab); // Debug log
    set({
      tabs,
      activeTab: defaultActiveTab,
      showTabs: tabs.length > 0
    });
  },

  setActiveTab: (tabId) => {
    const { tabs } = get();
    if (tabs?.find(tab => tab.id === tabId && !tab.disabled)) {
      set({ activeTab: tabId });
    }
  },

  updateTab: (tabId, updates) => {
    const { tabs } = get();
    if (!tabs) return;

    const updatedTabs = tabs.map(tab =>
      tab.id === tabId ? { ...tab, ...updates } : tab
    );

    set({ tabs: updatedTabs });
  },

  hideTabs: () => set({ tabs: undefined, activeTab: undefined, showTabs: false }),
}));